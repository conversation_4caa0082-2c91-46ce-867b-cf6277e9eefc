[project]
name = "cicflowmeter"
version = "0.4.2-ma"
description = "CICFlowMeter Python Implementation"
authors = [{ name = "<PERSON><PERSON> Le", email = "<EMAIL>" }]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.12"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "numpy>=1.26.2",
    "scipy>=1.11.4",
    "scapy>=2.5.0",
    "requests>=2.31.0",
]
changelog = "CHANGELOG.md"
homepage = "https://github.com/hieulw/cicflowmeter"
repository = "https://github.com/hieulw/cicflowmeter"

[project.scripts]
cicflowmeter = "cicflowmeter.sniffer:main"

[dependency-groups]
dev = [
    "pytest",
    "ruff",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
